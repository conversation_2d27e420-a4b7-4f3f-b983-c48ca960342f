# Call Flow Bug Fix

## Issue Description

We identified and fixed a critical bug in the calling functionality where the caller side remained in "Connecting..." state without establishing audio/video connection after the callee accepted the call. This indicated potential WebRTC or socket issues in the connection flow.

## Root Cause Analysis

After thorough investigation, we identified the following issues:

1. **Missing call_active Event**: The socket server wasn't properly emitting the `call_active` event after call acceptance if the caller had already sent their SDP offer.

2. **Conditional UI Update**: The frontend was only updating the UI state to "connected" when both the `call_active` event was received AND a local stream was available, causing the UI to remain in "Connecting..." state.

3. **WebRTC Connection State**: The WebRTC manager wasn't properly handling the `call_active` event to ensure the connection state was updated.

## Implemented Fixes

### 1. Socket Server (callingEvents.ts)

Added logic in the `handleAnswerCall` method to check if caller SDP data is already available when a call is answered. If so, it immediately sets the call to active and emits the `call_active` event to both participants.

```typescript
// Check if we already have caller SDP data, if so, set call to active immediately
const callData = await this.callingService.getCall(validatedData.callId);
if (callData && callData.callerSdp) {
  console.log(`📞 Call ${validatedData.callId} already has caller SDP, setting to active immediately`);
  await this.callingService.updateCallStatus(validatedData.callId, 'active');
  
  // Emit call_active event to both participants
  this.io.to(`call:${validatedData.callId}`).emit('call_active', {
    callId: validatedData.callId,
    timestamp: new Date().toISOString()
  });
}
```

### 2. Frontend WebRTC Manager (webrtc.ts)

Added a listener for the `call_active` event in the WebRTC manager to ensure the connection state is updated to "connected" when the event is received:

```typescript
this.socket.on('call_active', (data: { callId: string; timestamp: string }) => {
  console.log('🔌 Socket event received: call_active', data);
  if (data.callId === this.callId) {
    console.log('📞 Call is now active, ensuring connection state is updated');
    // Force connection state update to ensure UI reflects active call
    this.events.onConnectionStateChange?.('connected');
  }
});
```

### 3. Frontend Calling Context (CallingContext.tsx)

Modified the `call_active` event handler to always update the UI state to "connected" when the event is received, regardless of whether a local stream is available:

```typescript
socket.on('call_active', (data: { callId: string }) => {
  console.log('🔌 Socket event received: call_active', data);
  if (data.callId === state.currentCall?.callId) {
    console.log('📞 Call is now active, updating UI state');
    // Always update to connected state when call_active is received
    // This ensures the UI transitions from "Connecting..." to active call
    dispatch({ type: 'CALL_CONNECTED' });
  }
});
```

Also updated the reducer to handle the `CALL_CONNECTED` action properly even when localStream is null.

## Testing the Fix

We've created a test script (`test-call-flow-fix.js`) that simulates the call flow between a caller and callee. This script:

1. Connects to the socket server as both caller and callee
2. Initiates a call via the Django API
3. Simulates the callee answering the call via the Django API
4. Simulates WebRTC offer/answer exchange
5. Verifies that the `call_active` event is properly received by both participants

To run the test script:

```bash
node test-call-flow-fix.js
```

## Manual Testing Steps

1. Start the Django server and socket server
2. Log in as two different users in separate browser windows/tabs
3. Initiate a call from one user to the other
4. Accept the call on the receiving end
5. Verify that both users transition to the active call state with audio/video connection

## Verification

After implementing these fixes, the call flow should work as expected:

1. Caller initiates call → Callee receives incoming call notification
2. Callee accepts call → Both sides transition to "Connecting..." state
3. WebRTC offer/answer exchange occurs
4. Both sides receive `call_active` event and transition to active call state
5. Audio/video connection is established

## Additional Logging

We've added extensive logging throughout the call flow to help diagnose any future issues:

- Socket event reception and emission
- WebRTC offer/answer exchange
- Connection state changes
- Call status updates

These logs can be viewed in the browser console and server logs for debugging purposes.