// test-call-flow-fix.js
// This script tests and fixes the call flow issue where the caller side remains in "Connecting..." state

const socket = require('socket.io-client');
const axios = require('axios');

// Configuration
const SOCKET_SERVER_URL = 'http://localhost:3001'; // Adjust to your socket server URL
const DJANGO_API_URL = 'http://localhost:8000/api'; // Adjust to your Django API URL

// Test users (replace with actual test users from your system)
const CALLER_TOKEN = 'caller-auth-token'; // Replace with actual token
const CALLEE_TOKEN = 'callee-auth-token'; // Replace with actual token

// Connect to socket server as caller
const callerSocket = socket(SOCKET_SERVER_URL, {
  query: { token: CALLER_TOKEN },
  transports: ['websocket'],
});

// Connect to socket server as callee
const calleeSocket = socket(SOCKET_SERVER_URL, {
  query: { token: CALLEE_TOKEN },
  transports: ['websocket'],
});

// Track call state
let currentCallId = null;
let callerConnected = false;
let calleeConnected = false;

// Set up caller socket event listeners
callerSocket.on('connect', () => {
  console.log('🔌 Caller socket connected');
  callerConnected = true;
  if (callerConnected && calleeConnected) {
    startTest();
  }
});

callerSocket.on('call_initiated', (data) => {
  console.log('📞 Call initiated:', data);
  currentCallId = data.callId;
});

callerSocket.on('call_ringing', (data) => {
  console.log('📞 Call ringing:', data);
});

callerSocket.on('call_answered', (data) => {
  console.log('📞 Call answered:', data);
  // Simulate WebRTC offer after call is answered
  simulateWebRTCOffer();
});

callerSocket.on('call_active', (data) => {
  console.log('📞 Call active:', data);
  console.log('✅ TEST PASSED: call_active event received by caller');
});

callerSocket.on('webrtc_answer', (data) => {
  console.log('🔄 WebRTC answer received:', data);
});

// Set up callee socket event listeners
calleeSocket.on('connect', () => {
  console.log('🔌 Callee socket connected');
  calleeConnected = true;
  if (callerConnected && calleeConnected) {
    startTest();
  }
});

calleeSocket.on('incoming_call', (data) => {
  console.log('📞 Incoming call:', data);
  currentCallId = data.callId;
  // Simulate answering the call after a short delay
  setTimeout(() => answerCall(), 1000);
});

calleeSocket.on('webrtc_offer', (data) => {
  console.log('🔄 WebRTC offer received:', data);
  // Simulate WebRTC answer
  simulateWebRTCAnswer();
});

calleeSocket.on('call_active', (data) => {
  console.log('📞 Call active event received by callee:', data);
});

// Helper functions
async function startTest() {
  console.log('🧪 Starting call flow test...');
  try {
    // Initiate call via Django API
    const response = await axios.post(
      `${DJANGO_API_URL}/calling/initiate/`,
      {
        conversation_id: 'test-conversation-id', // Replace with actual conversation ID
        call_type: 'video',
      },
      {
        headers: {
          Authorization: `Bearer ${CALLER_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('📞 Call initiated via API:', response.data);
  } catch (error) {
    console.error('❌ Failed to initiate call:', error.response?.data || error.message);
  }
}

async function answerCall() {
  if (!currentCallId) {
    console.error('❌ No active call to answer');
    return;
  }

  try {
    // Answer call via Django API
    const response = await axios.post(
      `${DJANGO_API_URL}/calling/${currentCallId}/answer/`,
      {},
      {
        headers: {
          Authorization: `Bearer ${CALLEE_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('📞 Call answered via API:', response.data);
  } catch (error) {
    console.error('❌ Failed to answer call:', error.response?.data || error.message);
  }
}

function simulateWebRTCOffer() {
  if (!currentCallId) {
    console.error('❌ No active call for WebRTC offer');
    return;
  }

  // Create a mock SDP offer
  const mockOffer = {
    type: 'offer',
    sdp: 'v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0\r\na=msid-semantic: WMS\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:mock\r\na=ice-pwd:mockpwd\r\na=fingerprint:sha-256 AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99\r\na=setup:actpass\r\na=mid:0\r\na=sendrecv\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=fmtp:111 minptime=10;useinbandfec=1\r\n',
  };

  // Emit WebRTC offer
  callerSocket.emit('webrtc_offer', {
    callId: currentCallId,
    offer: mockOffer,
  });

  console.log('🔄 Simulated WebRTC offer sent');
}

function simulateWebRTCAnswer() {
  if (!currentCallId) {
    console.error('❌ No active call for WebRTC answer');
    return;
  }

  // Create a mock SDP answer
  const mockAnswer = {
    type: 'answer',
    sdp: 'v=0\r\no=- 987654321 1 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0\r\na=msid-semantic: WMS\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:mock\r\na=ice-pwd:mockpwd\r\na=fingerprint:sha-256 AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99\r\na=setup:active\r\na=mid:0\r\na=sendrecv\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=fmtp:111 minptime=10;useinbandfec=1\r\n',
  };

  // Emit WebRTC answer
  calleeSocket.emit('webrtc_answer', {
    callId: currentCallId,
    answer: mockAnswer,
  });

  console.log('🔄 Simulated WebRTC answer sent');
}

// Handle errors and disconnection
callerSocket.on('error', (error) => {
  console.error('❌ Caller socket error:', error);
});

calleeSocket.on('error', (error) => {
  console.error('❌ Callee socket error:', error);
});

callerSocket.on('disconnect', () => {
  console.log('🔌 Caller socket disconnected');
});

calleeSocket.on('disconnect', () => {
  console.log('🔌 Callee socket disconnected');
});

// Clean up on process exit
process.on('SIGINT', () => {
  console.log('Cleaning up and exiting...');
  callerSocket.disconnect();
  calleeSocket.disconnect();
  process.exit(0);
});