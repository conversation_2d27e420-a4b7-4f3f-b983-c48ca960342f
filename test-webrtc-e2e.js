#!/usr/bin/env node

/**
 * Complete WebRTC E2E Test Runner
 * This script sets up test users and runs the WebRTC calling tests
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🎯 WebRTC E2E Test Runner');
console.log('==========================\n');

// Check if all services are running
async function checkServices() {
  console.log('🔍 Checking if services are running...');
  
  const services = [
    { name: 'Backend (Django)', url: 'http://localhost:6000', port: 6000 },
    { name: 'Frontend (React)', url: 'http://localhost:5000', port: 5000 },
    { name: 'Socket Server', url: 'http://localhost:7000', port: 7000 }
  ];
  
  const axios = require('axios');
  
  for (const service of services) {
    try {
      await axios.get(service.url, { timeout: 2000 });
      console.log(`✅ ${service.name} is running on port ${service.port}`);
    } catch (error) {
      console.error(`❌ ${service.name} is not running on port ${service.port}`);
      console.error(`   Please start it first and try again.`);
      return false;
    }
  }
  
  return true;
}

// Run a command and return a promise
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

// Setup test users
async function setupTestUsers() {
  console.log('\n📝 Setting up test users...');
  try {
    await runCommand('node', ['e2e/scripts/setup-test-users.js']);
    console.log('✅ Test users setup complete');
  } catch (error) {
    console.error('❌ Failed to setup test users:', error.message);
    throw error;
  }
}

// Install E2E dependencies if needed
async function installE2EDependencies() {
  const e2eDir = path.join(__dirname, 'e2e');
  const packageJsonPath = path.join(e2eDir, 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.log('\n📦 Initializing E2E test directory...');
    await runCommand('npm', ['init', '-y'], { cwd: e2eDir });
  }
  
  const nodeModulesPath = path.join(e2eDir, 'node_modules');
  if (!fs.existsSync(nodeModulesPath)) {
    console.log('\n📦 Installing E2E dependencies...');
    await runCommand('npm', ['install', '@playwright/test', 'axios'], { cwd: e2eDir });
    await runCommand('npx', ['playwright', 'install'], { cwd: e2eDir });
  }
}

// Run the WebRTC tests
async function runWebRTCTests() {
  console.log('\n🧪 Running WebRTC E2E tests...');
  try {
    await runCommand('npx', ['playwright', 'test', 'webrtc-calling.spec.ts', '--headed'], {
      cwd: path.join(__dirname, 'e2e')
    });
    console.log('✅ WebRTC tests completed successfully');
  } catch (error) {
    console.error('❌ WebRTC tests failed:', error.message);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    // Check if services are running
    const servicesRunning = await checkServices();
    if (!servicesRunning) {
      console.log('\n💡 To start all services, run:');
      console.log('   npm run dev (in the root directory)');
      console.log('   or start each service individually:');
      console.log('   - Backend: cd backend && python manage.py runserver 6000');
      console.log('   - Frontend: cd frontend && npm run dev');
      console.log('   - Socket: cd socket-server && npm run dev');
      process.exit(1);
    }
    
    // Install E2E dependencies
    await installE2EDependencies();
    
    // Setup test users
    await setupTestUsers();
    
    // Run tests
    await runWebRTCTests();
    
    console.log('\n🎉 All WebRTC E2E tests completed successfully!');
    console.log('=============================================');
    
  } catch (error) {
    console.error('\n❌ E2E test execution failed:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log('WebRTC E2E Test Runner');
  console.log('');
  console.log('Usage: node test-webrtc-e2e.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --setup-only   Only setup test users, don\'t run tests');
  console.log('  --test-only    Only run tests, skip setup');
  console.log('');
  console.log('Prerequisites:');
  console.log('  - Backend server running on port 6000');
  console.log('  - Frontend server running on port 5000');
  console.log('  - Socket server running on port 7000');
  process.exit(0);
}

if (args.includes('--setup-only')) {
  checkServices().then(servicesRunning => {
    if (servicesRunning) {
      return setupTestUsers();
    } else {
      console.log('❌ Services not running');
      process.exit(1);
    }
  }).then(() => {
    console.log('✅ Setup complete');
  }).catch(error => {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  });
} else if (args.includes('--test-only')) {
  installE2EDependencies().then(() => {
    return runWebRTCTests();
  }).then(() => {
    console.log('✅ Tests complete');
  }).catch(error => {
    console.error('❌ Tests failed:', error.message);
    process.exit(1);
  });
} else {
  main();
}
