# backend/calling/urls.py
from django.urls import path
from . import views

urlpatterns = [
    # Call management endpoints
    path('initiate/', views.initiate_call, name='initiate_call'),
    path('<uuid:call_id>/answer/', views.answer_call, name='answer_call'),
    path('<uuid:call_id>/decline/', views.decline_call, name='decline_call'),
    path('<uuid:call_id>/end/', views.end_call, name='end_call'),
    
    # WebRTC signaling endpoints
    path('<uuid:call_id>/sdp/', views.update_call_sdp, name='update_call_sdp'),
    
    # Call quality and monitoring
    path('<uuid:call_id>/quality/', views.report_call_quality, name='report_call_quality'),
    
    # Call history and details
    path('history/', views.get_call_history, name='get_call_history'),
    path('<uuid:call_id>/', views.get_call_detail, name='get_call_detail'),
]
