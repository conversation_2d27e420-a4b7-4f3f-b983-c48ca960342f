console from caller

```bash
CallControls.tsx:80 🎯 Initiating audio call with real media access
CallingContext.tsx:532 🚀 [CALL_DEBUG] === INITIATING CALL ===
CallingContext.tsx:533 🚀 [CALL_DEBUG] Conversation ID: 3f8c9263-6fa1-49ad-b9f2-6b49c52d955f
CallingContext.tsx:534 🚀 [CALL_DEBUG] Call type: audio
CallingContext.tsx:535 🚀 [CALL_DEBUG] Callee: {id: '8d3a49c3-4e67-4be0-b207-06165531abec', username: '<EMAIL>', firstName: '', lastName: '', profilePicture: undefined}
CallingContext.tsx:536 🚀 [CALL_DEBUG] Current user: {id: 'a001b1f5-c91c-4817-8ede-a3816e2db424', email: '<EMAIL>', username: '<EMAIL>', firstName: 'harry', lastName: 'parot', …}
CallingContext.tsx:537 🔥 [CALL_DEBUG] Socket available: true
CallingContext.tsx:538 🔌 [CALL_DEBUG] Socket connected: true
CallingContext.tsx:539 🆔 [CALL_DEBUG] Socket ID: hxOQusL1HQu6bakgAACd
CallingContext.tsx:540 🏠 [CALL_DEBUG] Socket rooms: undefined
CallingContext.tsx:557 🎥 [CALL_DEBUG] REAL MODE: Initiating call with real WebRTC
CallingContext.tsx:595 🚀 [CALL_DEBUG] Created temporary call object: {id: 'temp', conversationId: '3f8c9263-6fa1-49ad-b9f2-6b49c52d955f', type: 'audio', status: 'initiating', isIncoming: false, …}
CallingContext.tsx:597 🚀 [CALL_DEBUG] Dispatched OUTGOING_CALL action
CallingContext.tsx:600 🚀 [CALL_DEBUG] Initializing WebRTC with pending call ID
webrtc.ts:364 [DEBUG] WebRTC initializeCall - Setting callId: pending (type: string)
webrtc.ts:398 🎥 Requesting real media access - Audio: true, Video: false
webrtc.ts:428 🎥 Requesting media with constraints: {audio: {…}, video: false}
webrtc.ts:430 🎥 Successfully obtained media stream: {audioTracks: 1, videoTracks: 0}
CallingContext.tsx:603 🚀 [CALL_DEBUG] WebRTC initialized successfully with pending ID
CallingContext.tsx:608 🚀 [CALL_DEBUG] WebRTC initialized with placeholder ID for outgoing call
CallingContext.tsx:611 🚀 [CALL_DEBUG] Calling backend API to initiate call
api.ts:31 🔑 [AUTH] Preparing headers for endpoint: initiateCall URL: /calling/initiate/ Token exists: true
api.ts:42 🔑 [AUTH] ✅ Added authorization header for: initiateCall
CallingContext.tsx:617 ✅ [CALL_DEBUG] Backend API response: {id: 'd538420e-5df6-4083-8439-671adc68ecce', conversation: '3f8c9263-6fa1-49ad-b9f2-6b49c52d955f', caller: {…}, callee: {…}, callType: 'audio', …}
CallingContext.tsx:618 ✅ [CALL_DEBUG] Call ID received: d538420e-5df6-4083-8439-671adc68ecce
CallingContext.tsx:622 ✅ [CALL_DEBUG] Dispatched CALL_INITIATED action
CallingContext.tsx:628 ✅ [CALL_DEBUG] Re-initializing WebRTC with actual call ID: d538420e-5df6-4083-8439-671adc68ecce
webrtc.ts:364 [DEBUG] WebRTC initializeCall - Setting callId: d538420e-5df6-4083-8439-671adc68ecce (type: string)
webrtc.ts:398 🎥 Requesting real media access - Audio: true, Video: false
webrtc.ts:428 🎥 Requesting media with constraints: {audio: {…}, video: false}
webrtc.ts:430 🎥 Successfully obtained media stream: {audioTracks: 1, videoTracks: 0}
CallingContext.tsx:630 ✅ [CALL_DEBUG] WebRTC re-initialized successfully with actual call ID
CallingContext.tsx:634 🚀 [CALL_DEBUG] Creating WebRTC offer
webrtc.ts:535 🔄 [WEBRTC_DEBUG] === CREATING WEBRTC OFFER ===
webrtc.ts:536 🔄 [WEBRTC_DEBUG] Call ID: d538420e-5df6-4083-8439-671adc68ecce
webrtc.ts:537 🔄 [WEBRTC_DEBUG] Peer connection available: true
webrtc.ts:538 🔄 [WEBRTC_DEBUG] Local stream available: true
webrtc.ts:546 🔄 [WEBRTC_DEBUG] Peer connection state before offer: new
webrtc.ts:547 🔄 [WEBRTC_DEBUG] ICE connection state before offer: new
webrtc.ts:548 🔄 [WEBRTC_DEBUG] Signaling state before offer: stable
webrtc.ts:551 🔄 [WEBRTC_DEBUG] Local stream has video tracks: false
webrtc.ts:552 🔄 [WEBRTC_DEBUG] Local stream audio tracks: 1
webrtc.ts:559 🔄 [WEBRTC_DEBUG] Offer options: {offerToReceiveAudio: true, offerToReceiveVideo: true}
webrtc.ts:562 ✅ [WEBRTC_DEBUG] WebRTC offer created successfully
webrtc.ts:563 📋 [WEBRTC_DEBUG] Offer SDP type: offer
webrtc.ts:565 📤 [WEBRTC_DEBUG] Setting local description...
webrtc.ts:567 ✅ [WEBRTC_DEBUG] Local description set successfully
webrtc.ts:568 🔄 [WEBRTC_DEBUG] Signaling state after setting local description: have-local-offer
webrtc.ts:576 📡 [WEBRTC_DEBUG] Preparing to emit webrtc_offer via socket
webrtc.ts:577 📡 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:589 📡 [WEBRTC_DEBUG] Offer data to emit: {callId: 'd538420e-5df6-4083-8439-671adc68ecce', offer: {…}}
webrtc.ts:592 ✅ [WEBRTC_DEBUG] WebRTC offer emitted via socket
webrtc.ts:594 🔄 [WEBRTC_DEBUG] === WEBRTC OFFER CREATION COMPLETE ===
CallingContext.tsx:637 ✅ [CALL_DEBUG] WebRTC offer created successfully
CallingContext.tsx:640 🚀 [CALL_DEBUG] === CALL INITIATION COMPLETE ===
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:501 ICE gathering state for call d538420e-5df6-4083-8439-671adc68ecce: gathering
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
webrtc.ts:465 [DEBUG] Sending ICE candidate with callId: d538420e-5df6-4083-8439-671adc68ecce {callId: 'd538420e-5df6-4083-8439-671adc68ecce', candidate: RTCIceCandidate}
webrtc.ts:346 🧊 [WEBRTC_DEBUG] Preparing to emit ICE candidate
webrtc.ts:347 🧊 [WEBRTC_DEBUG] Socket connected: true
webrtc.ts:356 ✅ [WEBRTC_DEBUG] ICE candidate emitted via socket
CallingContext.tsx:371 ✅ [CALL_DEBUG] Call answered event received: {callId: 'd538420e-5df6-4083-8439-671adc68ecce', status: 'answered', timestamp: '2025-09-12T15:59:57.855267+00:00', userId: null}
CallingContext.tsx:372 ✅ [CALL_DEBUG] Current call state before answer: undefined
CallingContext.tsx:374 ✅ [CALL_DEBUG] Dispatched CALL_ANSWERED action
```

console from callee 

```bash
webrtc.ts:232 🔌 [WEBRTC_DEBUG] === SETTING UP SOCKET LISTENERS ===
webrtc.ts:233 🔌 [WEBRTC_DEBUG] Socket instance available: true
webrtc.ts:234 🔌 [WEBRTC_DEBUG] Socket connected: false
webrtc.ts:235 🔌 [WEBRTC_DEBUG] Socket ID: undefined
webrtc.ts:236 🔌 [WEBRTC_DEBUG] Current call ID: null
webrtc.ts:244 🔌 [WEBRTC_DEBUG] Registering webrtc_offer listener
webrtc.ts:257 🔌 [WEBRTC_DEBUG] Registering webrtc_answer listener
webrtc.ts:270 🔌 [WEBRTC_DEBUG] Registering webrtc_ice_candidate listener
webrtc.ts:284 🔌 [WEBRTC_DEBUG] Registering call_active listener
webrtc.ts:302 🔌 [WEBRTC_DEBUG] Registering socket connection status listeners
webrtc.ts:316 ✅ [WEBRTC_DEBUG] === SOCKET LISTENERS SETUP COMPLETE ===
CallingContext.tsx:280 🎥 REAL WEBRTC MODE ENABLED for production
webrtc.ts:232 🔌 [WEBRTC_DEBUG] === SETTING UP SOCKET LISTENERS ===
webrtc.ts:233 🔌 [WEBRTC_DEBUG] Socket instance available: true
webrtc.ts:234 🔌 [WEBRTC_DEBUG] Socket connected: false
webrtc.ts:235 🔌 [WEBRTC_DEBUG] Socket ID: undefined
webrtc.ts:236 🔌 [WEBRTC_DEBUG] Current call ID: null
webrtc.ts:244 🔌 [WEBRTC_DEBUG] Registering webrtc_offer listener
webrtc.ts:257 🔌 [WEBRTC_DEBUG] Registering webrtc_answer listener
webrtc.ts:270 🔌 [WEBRTC_DEBUG] Registering webrtc_ice_candidate listener
webrtc.ts:284 🔌 [WEBRTC_DEBUG] Registering call_active listener
webrtc.ts:302 🔌 [WEBRTC_DEBUG] Registering socket connection status listeners
webrtc.ts:316 ✅ [WEBRTC_DEBUG] === SOCKET LISTENERS SETUP COMPLETE ===
api.ts:31 🔑 [AUTH] Preparing headers for endpoint: getConversations URL: /messaging/conversations/ Token exists: true
api.ts:42 🔑 [AUTH] ✅ Added authorization header for: getConversations
Dashboard.tsx:21 Auth State: {user: {…}, authLoading: false}
Dashboard.tsx:21 Auth State: {user: {…}, authLoading: false}
SocketContext.tsx:193 🔌 [SOCKET_DEBUG] === SOCKET CONNECTED ===
SocketContext.tsx:194 🔌 [SOCKET_DEBUG] Socket ID: nUIRcdMB6V1ucH8MAACf
SocketContext.tsx:195 🔌 [SOCKET_DEBUG] Socket connected: true
SocketContext.tsx:196 🔌 [SOCKET_DEBUG] User authenticated: false
SocketContext.tsx:197 🔌 [SOCKET_DEBUG] User ID: undefined
SocketContext.tsx:202 🔌 [SOCKET_DEBUG] Emitting join_conversations
SocketContext.tsx:204 🔌 [SOCKET_DEBUG] Emitting user_online
SocketContext.tsx:208 ✅ [SOCKET_DEBUG] Socket connection setup complete
webrtc.ts:304 ✅ [WEBRTC_DEBUG] Socket connected successfully
webrtc.ts:305 🔌 [WEBRTC_DEBUG] Socket ID: nUIRcdMB6V1ucH8MAACf
webrtc.ts:304 ✅ [WEBRTC_DEBUG] Socket connected successfully
webrtc.ts:305 🔌 [WEBRTC_DEBUG] Socket ID: nUIRcdMB6V1ucH8MAACf
Dashboard.tsx:21 Auth State: {user: {…}, authLoading: false}
SocketContext.tsx:233 Joined 1 conversations
CallingContext.tsx:344 🔔 [CALL_DEBUG] Incoming call received: {callId: 'd538420e-5df6-4083-8439-671adc68ecce', caller: {…}, callType: 'audio', conversationId: '3f8c9263-6fa1-49ad-b9f2-6b49c52d955f', timestamp: '2025-09-12T15:59:53.531656+00:00'}
CallingContext.tsx:345 🔔 [CALL_DEBUG] Call data structure: {
  "callId": "d538420e-5df6-4083-8439-671adc68ecce",
  "caller": {
    "id": "a001b1f5-c91c-4817-8ede-a3816e2db424",
    "username": "<EMAIL>",
    "firstName": "harry",
    "lastName": "parot",
    "profilePicture": null
  },
  "callType": "audio",
  "conversationId": "3f8c9263-6fa1-49ad-b9f2-6b49c52d955f",
  "timestamp": "2025-09-12T15:59:53.531656+00:00"
}
CallingContext.tsx:365 🔔 [CALL_DEBUG] Incoming call - Type: audio Full data: {callId: 'd538420e-5df6-4083-8439-671adc68ecce', caller: {…}, callType: 'audio', conversationId: '3f8c9263-6fa1-49ad-b9f2-6b49c52d955f', timestamp: '2025-09-12T15:59:53.531656+00:00'}
CallingContext.tsx:650 📞 [CALL_DEBUG] === ANSWERING CALL ===
CallingContext.tsx:651 📞 [CALL_DEBUG] Active call: {id: 'd538420e-5df6-4083-8439-671adc68ecce', conversationId: '3f8c9263-6fa1-49ad-b9f2-6b49c52d955f', type: 'audio', status: 'ringing', isIncoming: true, …}
CallingContext.tsx:670 🎥 [CALL_DEBUG] REAL MODE: Answering call with real WebRTC
CallingContext.tsx:673 📞 [CALL_DEBUG] Call ID to answer: d538420e-5df6-4083-8439-671adc68ecce
CallingContext.tsx:674 📞 [CALL_DEBUG] Call type: audio
CallingContext.tsx:677 📞 [CALL_DEBUG] Calling backend API to answer call
api.ts:31 🔑 [AUTH] Preparing headers for endpoint: answerCall URL: /calling/d538420e-5df6-4083-8439-671adc68ecce/answer/ Token exists: true
api.ts:42 🔑 [AUTH] ✅ Added authorization header for: answerCall
CallingContext.tsx:679 ✅ [CALL_DEBUG] Backend API call successful
CallingContext.tsx:684 📞 [CALL_DEBUG] Dispatching CALL_ANSWERED action
CallingContext.tsx:686 ✅ [CALL_DEBUG] CALL_ANSWERED action dispatched
CallingContext.tsx:689 📞 [CALL_DEBUG] Initializing WebRTC for incoming call
webrtc.ts:364 [DEBUG] WebRTC initializeCall - Setting callId: d538420e-5df6-4083-8439-671adc68ecce (type: string)
webrtc.ts:398 🎥 Requesting real media access - Audio: true, Video: false
webrtc.ts:428 🎥 Requesting media with constraints: {audio: {…}, video: false}
webrtc.ts:430 🎥 Successfully obtained media stream: {audioTracks: 1, videoTracks: 0}
CallingContext.tsx:693 ✅ [CALL_DEBUG] WebRTC initialized successfully
CallingContext.tsx:697 📞 [CALL_DEBUG] Local stream obtained: true
CallingContext.tsx:700 ✅ [CALL_DEBUG] WebRTC initialized for incoming call, waiting for offer
CallingContext.tsx:705 ✅ [CALL_DEBUG] Local stream obtained, dispatching LOCAL_STREAM_READY
CallingContext.tsx:719 📞 [CALL_DEBUG] === CALL ANSWER COMPLETE ===

```