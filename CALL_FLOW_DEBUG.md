# Call Flow Debugging Guide

## Issue Fixed

We've fixed an issue where the caller remained in "Connecting..." state after the callee accepted the call, and the callee had no WebRTC connection established. The root causes were:

1. The `call_active` event was not being properly processed by both parties
2. The callee was prematurely transitioning to the connected state before receiving a WebRTC offer
3. ICE candidates were not being properly validated and processed
4. Room membership in the socket server was not being properly managed

## Changes Made

1. Added a new `LOCAL_STREAM_READY` action to separate local stream initialization from call connection
2. Updated the callee's call acceptance flow to wait for the WebRTC offer before transitioning to connected state
3. Added validation for ICE candidates and SDP offers/answers to ensure they match the current call
4. Added extensive logging to track WebRTC connection state changes
5. Added a debug endpoint to the socket server to check room membership
6. Added logging for call room membership when emitting the `call_active` event

## Testing the Fix

We've created a test script (`test-call-flow-debug.js`) that simulates the call flow between two users. This script helps diagnose issues with the WebRTC connection establishment.

### Prerequisites

1. Install required dependencies:
   ```
   npm install socket.io-client axios
   ```

2. Update the script with your actual test user tokens and API endpoints

### Running the Test

```
node test-call-flow-debug.js
```

The script will:
1. Connect to the socket server as both caller and callee
2. Initiate a call via the Django API
3. Simulate the callee answering the call
4. Exchange WebRTC offers, answers, and ICE candidates
5. Log the entire process, including room membership

## Debugging Tips

1. **Check Socket Room Membership**: Use the debug endpoint to verify that both caller and callee are in the call room:
   ```javascript
   socket.emit('debug_room_members', { room: `call:${callId}` }, (response) => {
     console.log('Room members:', response);
   });
   ```

2. **Monitor WebRTC Connection States**: Look for these log messages in the browser console:
   - `WebRTC connection state changed for call ${callId}: ${state}`
   - `ICE connection state for call ${callId}: ${state}`
   - `ICE gathering state for call ${callId}: ${state}`

3. **Verify SDP Exchange**: Ensure that offers and answers are being properly exchanged:
   - `Creating WebRTC offer for call ${callId}...`
   - `Received WebRTC offer for call ${callId}`
   - `Answer created for call ${callId}`
   - `Received WebRTC answer for call ${callId}`

4. **Check ICE Candidates**: Verify that ICE candidates are being properly forwarded and added:
   - `Adding ICE candidate for call ${callId}`
   - `Successfully added ICE candidate`

5. **Verify Call Active Event**: Check that the `call_active` event is being emitted and received by both parties:
   - `Emitting call_active event to room: ${callRoom}`
   - `Call active event received by caller/callee`

## Common Issues

1. **Mismatched Call IDs**: Ensure that the same call ID is used throughout the entire call flow
2. **Missing Room Membership**: Both caller and callee must join the call room for signaling to work
3. **Premature State Transitions**: Wait for the appropriate events before transitioning call states
4. **ICE Candidate Timing**: ICE candidates must be processed after the remote description is set
5. **STUN/TURN Server Issues**: Ensure that your ICE servers are properly configured

## Next Steps

If you continue to experience issues with the call flow, consider:

1. Implementing a more robust state machine for call states
2. Adding retry logic for failed WebRTC connections
3. Implementing fallback mechanisms for direct connections
4. Adding more comprehensive logging and monitoring
5. Setting up automated tests for the call flow