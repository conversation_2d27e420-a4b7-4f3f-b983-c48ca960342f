// frontend/src/components/Chat/ChatHeader.tsx
import React from 'react';
import { useSelector } from 'react-redux';
import { User, MoreVertical } from 'lucide-react';
// ...removed CallControls import...

import type { RootState } from '../../store';
import type { Conversation, DraftConversation } from '../../store/slices/conversationSlice';

interface ChatHeaderProps {
  conversationId: string;
  currentUserId: string;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({ conversationId, currentUserId }) => {
  const isDraftConversation = conversationId.startsWith('draft-');
  
  // Get conversation from Redux store for draft conversations
  const draftConversation = useSelector((state: RootState) => 
    state.conversations.draftConversations.find(draft => draft.id === conversationId)
  );
  
  // Get real conversation from Redux store
  const reduxConversation = useSelector((state: RootState) => 
    state.conversations.conversations.find(conv => conv.id === conversationId)
  );
  
  // Determine the conversation data - use Redux store only
  const conversation: Conversation | DraftConversation | undefined = isDraftConversation 
    ? draftConversation 
    : reduxConversation;
  
  const isLoading = false; // No API loading since we're using Redux store only

  console.log('conversation1111111111111', {
    conversationId,
    isDraftConversation,
    draftConversation,
    reduxConversation,
    finalConversation: conversation,
    isLoading
  });

  if (isLoading || !conversation) {
    return (
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
            <div>
              <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="w-16 h-3 bg-gray-200 rounded animate-pulse mt-1"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Get the display name for the conversation
  const getDisplayName = () => {
    if (conversation.type === 'GROUP') {
      return conversation.name || 'Group Chat';
    }
    return 'Direct message';
  };

  // Get the subtitle for the conversation
  const getSubtitle = () => {
    if (conversation.type === 'GROUP') {
      const participantCount = conversation.participants.length;
      return `${participantCount} members`;
    }
    if (isDraftConversation) {
      return 'New conversation';
    }
    return 'Direct message';
  };

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Conversation info */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
            <User className="w-6 h-6 text-gray-600" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">{getDisplayName()}</h2>
            <p className="text-sm text-gray-500">{getSubtitle()}</p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          {/* More options */}
          <button
            className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            title="More options"
          >
            <MoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};
