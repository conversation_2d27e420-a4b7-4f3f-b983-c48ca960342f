# Phase 6: Frontend Implementation - WebRTC Calling System

## Overview

This document outlines the frontend implementation requirements for integrating the verified WebRTC calling functionality from `calling-test.html` into our React/Redux production environment. The test file has demonstrated 100% working status for audio calls, video calls, proper media access termination, and synchronized media controls.

## HTML Test File Analysis

### Architecture Review

The `calling-test.html` file implements a comprehensive WebRTC calling system with the following key components:

#### 1. **Core Technologies**
- **Socket.IO Client**: Real-time communication for signaling
- **WebRTC APIs**: Peer-to-peer media streaming
- **MediaDevices API**: Camera and microphone access
- **RTCPeerConnection**: WebRTC connection management

#### 2. **Key Features Implemented**
- ✅ Audio/Video calling with full duplex communication
- ✅ Call initiation, answering, and declining
- ✅ Real-time media controls (mute/unmute, video on/off)
- ✅ Screen sharing capabilities
- ✅ Connection state monitoring
- ✅ Call duration tracking
- ✅ Proper media cleanup on call termination
- ✅ ICE candidate handling for NAT traversal
- ✅ API integration for call management

#### 3. **WebRTC Configuration**
```javascript
rtcConfig: {
    iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
    ]
}
```

#### 4. **Socket Events Handled**
- `connect/disconnect` - Connection management
- `call_initiated` - Incoming call notifications
- `call_answered/call_declined` - Call response handling
- `call_ended` - Call termination
- `webrtc_offer/webrtc_answer` - WebRTC signaling
- `webrtc_ice_candidate` - ICE candidate exchange
- `toggle_audio/toggle_video` - Media control synchronization

## Frontend Implementation Requirements

### 1. **Component Architecture**

#### Core Components Structure
```
src/
├── components/
│   ├── calling/
│   │   ├── CallManager.jsx           # Main calling orchestrator
│   │   ├── IncomingCallModal.jsx     # Incoming call UI
│   │   ├── ActiveCallInterface.jsx   # During-call controls
│   │   ├── CallControls.jsx          # Media control buttons
│   │   ├── VideoContainer.jsx        # Local/remote video display
│   │   └── CallStatusIndicator.jsx   # Connection status display
│   └── shared/
│       ├── MediaPermissionModal.jsx  # Permission requests
│       └── NotificationHandler.jsx   # Call notifications
```

#### Service Layer
```
src/
├── services/
│   ├── webrtc/
│   │   ├── WebRTCService.js          # WebRTC connection management
│   │   ├── MediaService.js           # Media stream handling
│   │   └── SignalingService.js       # Socket.IO signaling
│   └── api/
│       └── callingAPI.js             # REST API integration
```

### 2. **Redux State Management**

#### State Structure
```javascript
// Redux Store Structure
const callingState = {
  // Connection State
  isConnected: false,
  connectionStatus: 'disconnected', // 'connecting', 'connected', 'disconnected'
  
  // Call State
  currentCall: {
    id: null,
    type: null, // 'audio' | 'video'
    status: 'idle', // 'idle', 'initiating', 'ringing', 'active', 'ending'
    participants: [],
    startTime: null,
    duration: 0
  },
  
  // Media State
  localStream: null,
  remoteStream: null,
  mediaPermissions: {
    audio: false,
    video: false
  },
  
  // Control State
  controls: {
    isAudioMuted: false,
    isVideoMuted: false,
    isSpeakerOn: true,
    isScreenSharing: false
  },
  
  // UI State
  showIncomingCall: false,
  showActiveCall: false,
  incomingCallData: null,
  
  // Error State
  error: null,
  lastError: null
};
```

#### Required Actions
```javascript
// Connection Actions
CONNECT_SOCKET
DISCONNECT_SOCKET
SET_CONNECTION_STATUS

// Call Management Actions
INITIATE_CALL
RECEIVE_INCOMING_CALL
ANSWER_CALL
DECLINE_CALL
END_CALL
SET_CALL_STATUS
UPDATE_CALL_DURATION

// Media Actions
SET_LOCAL_STREAM
SET_REMOTE_STREAM
UPDATE_MEDIA_PERMISSIONS
TOGGLE_AUDIO
TOGGLE_VIDEO
TOGGLE_SPEAKER
START_SCREEN_SHARE
STOP_SCREEN_SHARE

// UI Actions
SHOW_INCOMING_CALL_MODAL
HIDE_INCOMING_CALL_MODAL
SHOW_ACTIVE_CALL_INTERFACE
HIDE_ACTIVE_CALL_INTERFACE

// Error Actions
SET_CALLING_ERROR
CLEAR_CALLING_ERROR
```

### 3. **WebRTC Service Implementation**

#### WebRTCService.js
```javascript
class WebRTCService {
  constructor() {
    this.peerConnection = null;
    this.localStream = null;
    this.remoteStream = null;
    this.rtcConfig = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    };
  }

  // Core Methods (from test file)
  async initializePeerConnection()
  async getUserMedia(constraints)
  async createOffer()
  async createAnswer()
  async handleRemoteOffer(offer)
  async handleRemoteAnswer(answer)
  async handleICECandidate(candidate)
  toggleAudioTrack()
  toggleVideoTrack()
  async startScreenShare()
  closePeerConnection()
}
```

#### MediaService.js
```javascript
class MediaService {
  // Media permission and stream management
  async requestMediaPermissions(constraints)
  async getLocalStream(type) // 'audio', 'video', 'screen'
  stopAllTracks(stream)
  toggleTrack(stream, trackType, enabled)
  getMediaConstraints(callType)
}
```

#### SignalingService.js
```javascript
class SignalingService {
  constructor(socket, store) {
    this.socket = socket;
    this.store = store;
    this.setupSocketListeners();
  }

  // Socket event handlers (from test file)
  setupSocketListeners()
  emitCallInitiation(data)
  emitCallAnswer(data)
  emitCallDecline(data)
  emitCallEnd(data)
  emitWebRTCOffer(data)
  emitWebRTCAnswer(data)
  emitICECandidate(data)
  emitMediaToggle(data)
}
```

### 4. **Component Implementation Details**

#### CallManager.jsx
```jsx
const CallManager = () => {
  const dispatch = useDispatch();
  const callingState = useSelector(state => state.calling);
  const webrtcService = useRef(new WebRTCService());
  const signalingService = useRef(null);

  // Initialize services
  useEffect(() => {
    signalingService.current = new SignalingService(socket, dispatch);
    return () => {
      webrtcService.current.closePeerConnection();
    };
  }, []);

  // Call management methods
  const initiateCall = async (recipientId, callType) => {
    // Implementation from test file logic
  };

  const answerCall = async () => {
    // Implementation from test file logic
  };

  const endCall = () => {
    // Implementation from test file logic
  };

  return (
    <>
      {callingState.showIncomingCall && <IncomingCallModal />}
      {callingState.showActiveCall && <ActiveCallInterface />}
    </>
  );
};
```

#### IncomingCallModal.jsx
```jsx
const IncomingCallModal = () => {
  const { incomingCallData } = useSelector(state => state.calling);
  const dispatch = useDispatch();

  const handleAnswer = () => {
    dispatch(answerCall());
  };

  const handleDecline = () => {
    dispatch(declineCall());
  };

  return (
    <Modal isOpen={true} className="incoming-call-modal">
      <div className="call-notification">
        <Avatar src={incomingCallData?.caller?.avatar} />
        <h3>{incomingCallData?.caller?.name}</h3>
        <p>Incoming {incomingCallData?.type} call...</p>
        <div className="call-actions">
          <Button onClick={handleDecline} variant="danger">
            Decline
          </Button>
          <Button onClick={handleAnswer} variant="success">
            Answer
          </Button>
        </div>
      </div>
    </Modal>
  );
};
```

#### ActiveCallInterface.jsx
```jsx
const ActiveCallInterface = () => {
  const callingState = useSelector(state => state.calling);
  const dispatch = useDispatch();

  return (
    <div className="active-call-interface">
      <CallStatusIndicator />
      <VideoContainer />
      <CallControls />
      <div className="call-info">
        <span className="call-duration">
          {formatDuration(callingState.currentCall.duration)}
        </span>
      </div>
    </div>
  );
};
```

#### VideoContainer.jsx
```jsx
const VideoContainer = () => {
  const localVideoRef = useRef();
  const remoteVideoRef = useRef();
  const { localStream, remoteStream, currentCall } = useSelector(state => state.calling);

  useEffect(() => {
    if (localStream && localVideoRef.current) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  useEffect(() => {
    if (remoteStream && remoteVideoRef.current) {
      remoteVideoRef.current.srcObject = remoteStream;
    }
  }, [remoteStream]);

  return (
    <div className="video-container">
      <video 
        ref={remoteVideoRef} 
        className="remote-video" 
        autoPlay 
        playsInline 
      />
      {currentCall.type === 'video' && (
        <video 
          ref={localVideoRef} 
          className="local-video" 
          autoPlay 
          playsInline 
          muted 
        />
      )}
    </div>
  );
};
```

#### CallControls.jsx
```jsx
const CallControls = () => {
  const { controls, currentCall } = useSelector(state => state.calling);
  const dispatch = useDispatch();

  return (
    <div className="call-controls">
      <Button 
        onClick={() => dispatch(toggleAudio())} 
        variant={controls.isAudioMuted ? 'danger' : 'primary'}
      >
        {controls.isAudioMuted ? '🔇' : '🎤'}
      </Button>
      
      {currentCall.type === 'video' && (
        <Button 
          onClick={() => dispatch(toggleVideo())} 
          variant={controls.isVideoMuted ? 'danger' : 'primary'}
        >
          {controls.isVideoMuted ? '📹' : '📹'}
        </Button>
      )}
      
      <Button 
        onClick={() => dispatch(toggleSpeaker())} 
        variant={controls.isSpeakerOn ? 'primary' : 'secondary'}
      >
        {controls.isSpeakerOn ? '🔊' : '🔇'}
      </Button>
      
      <Button 
        onClick={() => dispatch(startScreenShare())} 
        variant={controls.isScreenSharing ? 'success' : 'secondary'}
      >
        🖥️
      </Button>
      
      <Button 
        onClick={() => dispatch(endCall())} 
        variant="danger"
      >
        📞
      </Button>
    </div>
  );
};
```

### 5. **Integration Requirements**

#### Socket.IO Integration
```javascript
// In socket middleware or service
const setupCallingSocketEvents = (socket, store) => {
  socket.on('call_initiated', (data) => {
    store.dispatch(receiveIncomingCall(data));
    // Show notification
    showCallNotification(data);
  });

  socket.on('call_answered', (data) => {
    store.dispatch(setCallStatus('active'));
  });

  socket.on('call_declined', (data) => {
    store.dispatch(setCallStatus('idle'));
    store.dispatch(clearCurrentCall());
  });

  socket.on('call_ended', (data) => {
    store.dispatch(endCall());
  });

  // WebRTC signaling events
  socket.on('webrtc_offer', async (data) => {
    await webrtcService.handleRemoteOffer(data.offer);
  });

  socket.on('webrtc_answer', async (data) => {
    await webrtcService.handleRemoteAnswer(data.answer);
  });

  socket.on('webrtc_ice_candidate', async (data) => {
    await webrtcService.handleICECandidate(data.candidate);
  });

  // Media control events
  socket.on('toggle_audio', (data) => {
    store.dispatch(updateRemoteAudioStatus(data.enabled));
  });

  socket.on('toggle_video', (data) => {
    store.dispatch(updateRemoteVideoStatus(data.enabled));
  });
};
```

#### API Integration
```javascript
// callingAPI.js
export const callingAPI = {
  getCallDetail: (callId) => 
    api.get(`/calling/${callId}/`),
  
  getCallHistory: (conversationId) => 
    api.get(`/calling/history/?conversation_id=${conversationId}`),
  
  reportCallQuality: (callId, qualityData) => 
    api.post(`/calling/${callId}/quality/`, qualityData),
  
  updateCallStatus: (callId, status) => 
    api.patch(`/calling/${callId}/`, { status })
};
```

### 6. **Styling Requirements**

#### CSS Structure
```scss
// calling.scss
.incoming-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.active-call-interface {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #000;
  display: flex;
  flex-direction: column;
  z-index: 9998;
}

.video-container {
  position: relative;
  flex: 1;
  
  .remote-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .local-video {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 200px;
    height: 150px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid #fff;
  }
}

.call-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.7);
  
  button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .local-video {
    width: 120px;
    height: 90px;
    top: 10px;
    right: 10px;
  }
  
  .call-controls {
    padding: 15px;
    
    button {
      width: 45px;
      height: 45px;
      font-size: 18px;
    }
  }
}
```

### 7. **Error Handling & Edge Cases**

#### Error Scenarios to Handle
1. **Media Permission Denied**
   - Show permission request modal
   - Graceful degradation (audio-only if video denied)
   - Clear error messaging

2. **Network Connection Issues**
   - Connection timeout handling
   - Reconnection attempts
   - ICE connection failure recovery

3. **WebRTC Compatibility**
   - Browser support detection
   - Fallback mechanisms
   - Feature detection

4. **Call State Conflicts**
   - Multiple incoming calls
   - Call interruption handling
   - Proper cleanup on errors

### 8. **Socket Reconnection & Network Resilience**

#### Exponential Backoff Reconnection Strategy
```javascript
class ReconnectionManager {
  constructor(socket, maxRetries = 10) {
    this.socket = socket;
    this.maxRetries = maxRetries;
    this.retryCount = 0;
    this.baseDelay = 1000; // 1 second
    this.maxDelay = 30000; // 30 seconds
    this.reconnectTimer = null;
    this.isReconnecting = false;
  }

  calculateBackoffDelay() {
    const exponentialDelay = this.baseDelay * Math.pow(2, this.retryCount);
    const jitter = Math.random() * 0.1 * exponentialDelay;
    return Math.min(exponentialDelay + jitter, this.maxDelay);
  }

  async attemptReconnection() {
    if (this.isReconnecting || this.retryCount >= this.maxRetries) {
      return false;
    }

    this.isReconnecting = true;
    const delay = this.calculateBackoffDelay();
    
    console.log(`Reconnection attempt ${this.retryCount + 1}/${this.maxRetries} in ${delay}ms`);
    
    return new Promise((resolve) => {
      this.reconnectTimer = setTimeout(async () => {
        try {
          await this.socket.connect();
          this.resetReconnection();
          resolve(true);
        } catch (error) {
          this.retryCount++;
          this.isReconnecting = false;
          resolve(false);
        }
      }, delay);
    });
  }

  resetReconnection() {
    this.retryCount = 0;
    this.isReconnecting = false;
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  stopReconnection() {
    this.resetReconnection();
    this.retryCount = this.maxRetries;
  }
}
```

#### Network State Management
```javascript
class NetworkStateManager {
  constructor(dispatch) {
    this.dispatch = dispatch;
    this.isOnline = navigator.onLine;
    this.setupNetworkListeners();
  }

  setupNetworkListeners() {
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
  }

  handleOnline() {
    this.isOnline = true;
    this.dispatch({ type: 'NETWORK_STATUS_CHANGED', payload: { online: true } });
    // Trigger reconnection if socket is disconnected
    if (!this.socket.connected) {
      this.reconnectionManager.attemptReconnection();
    }
  }

  handleOffline() {
    this.isOnline = false;
    this.dispatch({ type: 'NETWORK_STATUS_CHANGED', payload: { online: false } });
    // Pause call attempts and show offline indicator
    this.dispatch({ type: 'SET_CALLING_ERROR', payload: 'Network connection lost' });
  }
}
```

### 9. **Server-Side Idempotency & Call State Management**

#### Idempotent Call Operations
```javascript
class IdempotentCallManager {
  constructor(apiService) {
    this.apiService = apiService;
    this.pendingOperations = new Map();
    this.operationTimeouts = new Map();
  }

  async initiateCallIdempotent(recipientId, callType, idempotencyKey = null) {
    const key = idempotencyKey || `call-${recipientId}-${Date.now()}`;
    
    // Check if operation is already pending
    if (this.pendingOperations.has(key)) {
      return this.pendingOperations.get(key);
    }

    const operationPromise = this.performCallInitiation(recipientId, callType, key);
    this.pendingOperations.set(key, operationPromise);

    // Set timeout for operation cleanup
    const timeout = setTimeout(() => {
      this.pendingOperations.delete(key);
      this.operationTimeouts.delete(key);
    }, 30000); // 30 second timeout

    this.operationTimeouts.set(key, timeout);

    try {
      const result = await operationPromise;
      return result;
    } finally {
      // Cleanup
      this.pendingOperations.delete(key);
      const timeoutId = this.operationTimeouts.get(key);
      if (timeoutId) {
        clearTimeout(timeoutId);
        this.operationTimeouts.delete(key);
      }
    }
  }

  async performCallInitiation(recipientId, callType, idempotencyKey) {
    const payload = {
      recipient_id: recipientId,
      call_type: callType,
      idempotency_key: idempotencyKey,
      timestamp: new Date().toISOString()
    };

    return await this.apiService.post('/api/calls/initiate/', payload, {
      headers: {
        'Idempotency-Key': idempotencyKey
      }
    });
  }

  // Handle duplicate call responses
  handleCallResponse(callId, response) {
    const existingCall = this.getActiveCall();
    
    if (existingCall && existingCall.id === callId) {
      // Duplicate response for existing call - ignore
      console.log(`Ignoring duplicate response for call ${callId}`);
      return false;
    }

    return true; // Process the response
  }
}
```

#### ICE Candidate Queuing
```javascript
class ICECandidateQueue {
  constructor() {
    this.candidateQueue = [];
    this.peerConnectionReady = false;
  }

  queueCandidate(candidate) {
    if (this.peerConnectionReady) {
      this.addCandidateDirectly(candidate);
    } else {
      this.candidateQueue.push(candidate);
      console.log(`Queued ICE candidate. Queue size: ${this.candidateQueue.length}`);
    }
  }

  setPeerConnectionReady(peerConnection) {
    this.peerConnectionReady = true;
    this.peerConnection = peerConnection;
    this.flushCandidateQueue();
  }

  async flushCandidateQueue() {
    console.log(`Flushing ${this.candidateQueue.length} queued ICE candidates`);
    
    while (this.candidateQueue.length > 0) {
      const candidate = this.candidateQueue.shift();
      await this.addCandidateDirectly(candidate);
    }
  }

  async addCandidateDirectly(candidate) {
    try {
      if (this.peerConnection && this.peerConnection.remoteDescription) {
        await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
        console.log('ICE candidate added successfully');
      } else {
        // Re-queue if remote description not set yet
        this.candidateQueue.unshift(candidate);
      }
    } catch (error) {
      console.error('Failed to add ICE candidate:', error);
    }
  }

  clearQueue() {
    this.candidateQueue = [];
    this.peerConnectionReady = false;
  }
}
```

### 10. **PeerConnection Lifecycle & Track Management**

#### Robust PeerConnection Management
```javascript
class RobustWebRTCService {
  constructor() {
    this.peerConnection = null;
    this.localStream = null;
    this.remoteStream = null;
    this.senders = new Map(); // Track senders by track type
    this.transceivers = new Map(); // Track transceivers
    this.negotiationInProgress = false;
    this.pendingNegotiation = false;
    this.iceGatheringComplete = false;
  }

  async initializePeerConnection() {
    if (this.peerConnection) {
      this.closePeerConnection();
    }

    this.peerConnection = new RTCPeerConnection(this.rtcConfig);
    this.setupPeerConnectionEventHandlers();
    this.iceGatheringComplete = false;
    
    return this.peerConnection;
  }

  setupPeerConnectionEventHandlers() {
    this.peerConnection.addEventListener('negotiationneeded', this.handleNegotiationNeeded.bind(this));
    this.peerConnection.addEventListener('icecandidate', this.handleICECandidate.bind(this));
    this.peerConnection.addEventListener('icegatheringstatechange', this.handleICEGatheringStateChange.bind(this));
    this.peerConnection.addEventListener('connectionstatechange', this.handleConnectionStateChange.bind(this));
    this.peerConnection.addEventListener('track', this.handleRemoteTrack.bind(this));
  }

  async handleNegotiationNeeded() {
    if (this.negotiationInProgress) {
      this.pendingNegotiation = true;
      return;
    }

    this.negotiationInProgress = true;
    
    try {
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);
      
      // Send offer through signaling
      this.signalingService.emitWebRTCOffer({
        call_id: this.currentCallId,
        offer: offer
      });
      
    } catch (error) {
      console.error('Negotiation failed:', error);
    } finally {
      this.negotiationInProgress = false;
      
      if (this.pendingNegotiation) {
        this.pendingNegotiation = false;
        // Retry negotiation
        setTimeout(() => this.handleNegotiationNeeded(), 100);
      }
    }
  }

  async replaceTrackSafely(trackType, newTrack) {
    const sender = this.senders.get(trackType);
    
    if (!sender) {
      console.error(`No sender found for track type: ${trackType}`);
      return false;
    }

    try {
      // Create rollback point
      const currentTrack = sender.track;
      
      await sender.replaceTrack(newTrack);
      
      // Update local tracking
      this.senders.set(trackType, sender);
      
      console.log(`Successfully replaced ${trackType} track`);
      return true;
      
    } catch (error) {
      console.error(`Failed to replace ${trackType} track:`, error);
      
      // Attempt rollback
      try {
        await sender.replaceTrack(currentTrack);
        console.log(`Rolled back ${trackType} track replacement`);
      } catch (rollbackError) {
        console.error(`Rollback failed for ${trackType}:`, rollbackError);
      }
      
      return false;
    }
  }

  async addTrackWithTransceiver(track, stream, trackType) {
    try {
      // Check if transceiver already exists
      let transceiver = this.transceivers.get(trackType);
      
      if (transceiver) {
        // Replace existing track
        await this.replaceTrackSafely(trackType, track);
      } else {
        // Add new transceiver
        transceiver = this.peerConnection.addTransceiver(track, {
          direction: 'sendrecv',
          streams: [stream]
        });
        
        this.transceivers.set(trackType, transceiver);
        this.senders.set(trackType, transceiver.sender);
      }
      
      return transceiver;
      
    } catch (error) {
      console.error(`Failed to add ${trackType} track:`, error);
      throw error;
    }
  }

  handleICEGatheringStateChange() {
    const state = this.peerConnection.iceGatheringState;
    console.log(`ICE gathering state: ${state}`);
    
    if (state === 'complete') {
      this.iceGatheringComplete = true;
    }
  }

  handleConnectionStateChange() {
    const state = this.peerConnection.connectionState;
    console.log(`Connection state: ${state}`);
    
    switch (state) {
      case 'connected':
        this.dispatch({ type: 'CALL_CONNECTED' });
        break;
      case 'disconnected':
        this.handleDisconnection();
        break;
      case 'failed':
        this.handleConnectionFailure();
        break;
    }
  }

  async handleDisconnection() {
    // Attempt ICE restart
    try {
      const offer = await this.peerConnection.createOffer({ iceRestart: true });
      await this.peerConnection.setLocalDescription(offer);
      
      this.signalingService.emitWebRTCOffer({
        call_id: this.currentCallId,
        offer: offer,
        iceRestart: true
      });
      
    } catch (error) {
      console.error('ICE restart failed:', error);
      this.handleConnectionFailure();
    }
  }

  handleConnectionFailure() {
    this.dispatch({ 
      type: 'CALL_CONNECTION_FAILED',
      payload: 'Connection lost - attempting to reconnect'
    });
    
    // Trigger call recovery or termination
    setTimeout(() => {
      if (this.peerConnection.connectionState === 'failed') {
        this.endCall('Connection failed');
      }
    }, 5000);
  }

  closePeerConnection() {
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    
    // Clear tracking maps
    this.senders.clear();
    this.transceivers.clear();
    this.negotiationInProgress = false;
    this.pendingNegotiation = false;
    this.iceGatheringComplete = false;
  }
}
```

### 11. **Performance Considerations**

#### Optimization Strategies
1. **Memory Management**
   - Proper stream cleanup
   - Component unmounting cleanup
   - Event listener removal
   - PeerConnection resource cleanup

2. **Network Optimization**
   - Adaptive bitrate
   - Quality adjustment based on connection
   - Efficient ICE candidate handling
   - Connection state monitoring

3. **UI Performance**
   - Minimize re-renders during calls
   - Efficient video element handling
   - Debounced control updates
   - Optimized reconnection UI feedback

### 12. **Testing Strategy**

#### Unit Tests
- WebRTC service methods
- Redux actions and reducers
- Component rendering and interactions
- Reconnection manager logic
- Idempotent call operations
- ICE candidate queuing
- PeerConnection lifecycle management

#### Integration Tests
- Socket.IO event handling
- API integration
- Media stream management
- Network state transitions
- Exponential backoff behavior
- Track replacement scenarios

#### Resilience Tests
- Network disconnection/reconnection scenarios
- Server restart simulation
- Duplicate call initiation handling
- ICE candidate race conditions
- PeerConnection failure recovery
- Negotiation collision handling

#### E2E Tests
- Complete call flow
- Cross-browser compatibility
- Network condition simulation
- Call recovery after network issues
- Concurrent call handling
- Screen sharing track replacement

### 13. **Security Considerations**

#### Implementation Requirements
1. **Authentication**
   - JWT token validation for calls
   - User permission verification
   - Rate limiting for call attempts

2. **Media Security**
   - HTTPS requirement for getUserMedia
   - Secure WebRTC configuration
   - Media stream encryption

3. **Data Protection**
   - No sensitive data in logs
   - Secure signaling channel
   - Proper session cleanup

## Implementation Timeline

### Phase 6.1: Core Infrastructure (Week 1-2)
- Set up Redux store structure
- Implement basic WebRTC service layer
- Create signaling service
- Basic component structure
- Network state management setup

### Phase 6.2: Resilience & Reliability (Week 2-3)
- Implement reconnection manager with exponential backoff
- Add idempotent call operations
- ICE candidate queuing system
- Robust PeerConnection lifecycle management
- Track replacement and rollback strategies

### Phase 6.3: UI Components (Week 3-4)
- Implement calling components
- Add styling and responsive design
- Integrate with existing UI framework
- Media permission handling
- Connection status indicators

### Phase 6.4: Integration & Testing (Week 5-6)
- Socket.IO integration with resilience features
- API integration with idempotency
- Comprehensive testing including resilience scenarios
- Performance optimization
- Network condition simulation testing

### Phase 6.5: Polish & Deployment (Week 7)
- Error handling refinement
- Cross-browser testing
- Production resilience validation
- Documentation
- Production deployment

## Success Criteria

✅ **Functional Requirements**
- Audio/video calls work seamlessly
- Media controls function properly
- Call state management is reliable
- Proper cleanup on call termination
- Automatic reconnection after network issues
- Idempotent call operations prevent duplicates
- Robust track replacement for screen sharing

✅ **Performance Requirements**
- Call connection time < 3 seconds
- Smooth video playback (30fps)
- Minimal memory leaks
- Responsive UI during calls
- Reconnection within 30 seconds of network recovery
- ICE candidate processing without blocking

✅ **Resilience Requirements**
- Handles network disconnections gracefully
- Recovers from server restarts automatically
- Manages concurrent call attempts safely
- Prevents call state inconsistencies
- Implements exponential backoff for reconnections
- Queues ICE candidates until PeerConnection ready

✅ **Quality Requirements**
- Cross-browser compatibility
- Mobile responsiveness
- Accessibility compliance
- Comprehensive error handling
- Production-ready reliability
- Comprehensive logging for debugging

This implementation provides production-ready WebRTC calling functionality with enterprise-grade resilience, addressing all critical reliability concerns while maintaining the same level of functionality as demonstrated in the test file and following React/Redux best practices.