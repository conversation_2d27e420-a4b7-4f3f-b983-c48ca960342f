#!/usr/bin/env node
/**
 * Test Socket Events for Calling Functionality
 * This script tests the complete socket event flow for calling
 */

const io = require('socket.io-client');
const axios = require('axios').default;

const BASE_URL = 'http://localhost:6000/api';
const SOCKET_URL = 'http://localhost:7000';

// Test users credentials
const USER1 = { email: '<EMAIL>', password: 'TestPassword123!' };
const USER2 = { email: '<EMAIL>', password: 'TestPassword123!' };

let user1Token, user2Token, user1Id, user2Id, conversationId;
let user1Socket, user2Socket;

async function login(userCredentials) {
    try {
        const response = await axios.post(`${BASE_URL}/auth/login/`, userCredentials);
        return {
            token: response.data.data.tokens.access,
            userId: response.data.data.user.id
        };
    } catch (error) {
        console.error('Login failed:', error.response?.data || error.message);
        throw error;
    }
}

async function createConversation(token, participantId) {
    try {
        const response = await axios.post(`${BASE_URL}/messaging/conversations/create/`, {
            name: "",
            type: "DIRECT",
            participantIds: [participantId]
        }, {
            headers: { Authorization: `Bearer ${token}` }
        });
        return response.data.id;
    } catch (error) {
        console.error('Create conversation failed:', error.response?.data || error.message);
        throw error;
    }
}

function connectSocket(token, userId, username) {
    return new Promise((resolve, reject) => {
        const socket = io(SOCKET_URL, {
            auth: { token },
            transports: ['websocket']
        });

        socket.on('connect', () => {
            console.log(`✅ ${username} connected to socket server`);
            resolve(socket);
        });

        socket.on('connect_error', (error) => {
            console.error(`❌ ${username} socket connection failed:`, error);
            reject(error);
        });

        // Set up call event listeners
        socket.on('incoming_call', (data) => {
            console.log(`📞 ${username} received incoming_call:`, data);
        });

        socket.on('call_answered', (data) => {
            console.log(`📞 ${username} received call_answered:`, data);
        });

        socket.on('call_declined', (data) => {
            console.log(`📞 ${username} received call_declined:`, data);
        });

        socket.on('call_ended', (data) => {
            console.log(`📞 ${username} received call_ended:`, data);
        });

        socket.on('call_ringing', (data) => {
            console.log(`📞 ${username} received call_ringing:`, data);
        });
    });
}

async function initiateCall(token, conversationId, callType = 'audio') {
    try {
        const response = await axios.post(`${BASE_URL}/calling/initiate/`, {
            conversationId,
            callType
        }, {
            headers: { Authorization: `Bearer ${token}` }
        });
        return response.data.id;
    } catch (error) {
        console.error('Initiate call failed:', error.response?.data || error.message);
        throw error;
    }
}

async function answerCall(token, callId) {
    try {
        const response = await axios.post(`${BASE_URL}/calling/${callId}/answer/`, {}, {
            headers: { Authorization: `Bearer ${token}` }
        });
        return response.data;
    } catch (error) {
        console.error('Answer call failed:', error.response?.data || error.message);
        throw error;
    }
}

async function endCall(token, callId) {
    try {
        const response = await axios.post(`${BASE_URL}/calling/${callId}/end/`, {}, {
            headers: { Authorization: `Bearer ${token}` }
        });
        return response.data;
    } catch (error) {
        console.error('End call failed:', error.response?.data || error.message);
        throw error;
    }
}

async function testCallFlow() {
    console.log('🚀 Starting socket event test for calling functionality...\n');

    try {
        // Step 1: Login both users
        console.log('1️⃣ Logging in users...');
        const user1Auth = await login(USER1);
        const user2Auth = await login(USER2);
        
        user1Token = user1Auth.token;
        user2Token = user2Auth.token;
        user1Id = user1Auth.userId;
        user2Id = user2Auth.userId;
        
        console.log(`✅ User1 logged in: ${user1Id}`);
        console.log(`✅ User2 logged in: ${user2Id}\n`);

        // Step 2: Connect sockets
        console.log('2️⃣ Connecting sockets...');
        user1Socket = await connectSocket(user1Token, user1Id, 'User1');
        user2Socket = await connectSocket(user2Token, user2Id, 'User2');
        console.log('✅ Both users connected to socket server\n');

        // Step 3: Create conversation
        console.log('3️⃣ Creating conversation...');
        conversationId = await createConversation(user1Token, user2Id);
        console.log(`✅ Conversation created: ${conversationId}\n`);

        // Step 4: Test call initiation
        console.log('4️⃣ Testing call initiation...');
        const callId = await initiateCall(user1Token, conversationId);
        console.log(`✅ Call initiated: ${callId}`);
        
        // Wait for socket events
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Step 5: Test call answer
        console.log('\n5️⃣ Testing call answer...');
        await answerCall(user2Token, callId);
        console.log('✅ Call answered');
        
        // Wait for socket events
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Step 6: Test call end
        console.log('\n6️⃣ Testing call end...');
        await endCall(user1Token, callId);
        console.log('✅ Call ended');
        
        // Wait for socket events
        await new Promise(resolve => setTimeout(resolve, 2000));

        console.log('\n🎉 Socket event test completed successfully!');

    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        // Cleanup
        if (user1Socket) user1Socket.disconnect();
        if (user2Socket) user2Socket.disconnect();
        process.exit(0);
    }
}

// Run the test
testCallFlow();
