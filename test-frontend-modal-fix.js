#!/usr/bin/env node
/**
 * Test Frontend Modal Fix
 * This script tests if the ActiveCallModal now shows for the caller
 */

const io = require('socket.io-client');
const axios = require('axios').default;

const BASE_URL = 'http://localhost:6000/api';
const SOCKET_URL = 'http://localhost:7000';

// Test users credentials
const USER1 = { email: '<EMAIL>', password: 'TestPassword123!' };
const USER2 = { email: '<EMAIL>', password: 'TestPassword123!' };

let user1Token, user2Token, user1Id, user2Id, conversationId;

async function login(userCredentials) {
    try {
        const response = await axios.post(`${BASE_URL}/auth/login/`, userCredentials);
        return {
            token: response.data.data.tokens.access,
            userId: response.data.data.user.id
        };
    } catch (error) {
        console.error('Login failed:', error.response?.data || error.message);
        throw error;
    }
}

async function createConversation(token, participantId) {
    try {
        const response = await axios.post(`${BASE_URL}/messaging/conversations/create/`, {
            name: "",
            type: "DIRECT",
            participantIds: [participantId]
        }, {
            headers: { Authorization: `Bearer ${token}` }
        });
        return response.data.id;
    } catch (error) {
        console.error('Create conversation failed:', error.response?.data || error.message);
        throw error;
    }
}

async function initiateCall(token, conversationId, callType = 'audio') {
    try {
        const response = await axios.post(`${BASE_URL}/calling/initiate/`, {
            conversationId,
            callType
        }, {
            headers: { Authorization: `Bearer ${token}` }
        });
        return response.data.id;
    } catch (error) {
        console.error('Initiate call failed:', error.response?.data || error.message);
        throw error;
    }
}

async function answerCall(token, callId) {
    try {
        console.log(`🔄 Attempting to answer call ${callId}...`);
        const response = await axios.post(`${BASE_URL}/calling/${callId}/answer/`, {}, {
            headers: { Authorization: `Bearer ${token}` }
        });
        console.log('✅ Call answered successfully:', response.data);
        return response.data;
    } catch (error) {
        console.error('❌ Answer call failed:', error.response?.data || error.message);
        console.error('❌ Full error:', error.response?.status, error.response?.statusText);
        throw error;
    }
}

async function getCallDetail(token, callId) {
    try {
        const response = await axios.get(`${BASE_URL}/calling/${callId}/`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        return response.data;
    } catch (error) {
        console.error('Get call detail failed:', error.response?.data || error.message);
        throw error;
    }
}

async function testModalFix() {
    console.log('🚀 Testing frontend modal fix...\n');

    try {
        // Step 1: Login both users
        console.log('1️⃣ Logging in users...');
        const user1Auth = await login(USER1);
        const user2Auth = await login(USER2);
        
        user1Token = user1Auth.token;
        user2Token = user2Auth.token;
        user1Id = user1Auth.userId;
        user2Id = user2Auth.userId;
        
        console.log(`✅ User1 logged in: ${user1Id}`);
        console.log(`✅ User2 logged in: ${user2Id}\n`);

        // Step 2: Create conversation
        console.log('2️⃣ Creating conversation...');
        conversationId = await createConversation(user1Token, user2Id);
        console.log(`✅ Conversation created: ${conversationId}\n`);

        // Step 3: Test call initiation
        console.log('3️⃣ Testing call initiation...');
        const callId = await initiateCall(user1Token, conversationId);
        console.log(`✅ Call initiated: ${callId}`);
        
        // Step 4: Check call details immediately
        console.log('\n4️⃣ Checking call details immediately after initiation...');
        const callDetails = await getCallDetail(user1Token, callId);
        console.log('📋 Call details:', {
            id: callDetails.call.id,
            status: callDetails.call.status,
            caller: callDetails.call.caller.username,
            callee: callDetails.call.callee.username
        });

        // Wait a moment for status to potentially change to 'ringing'
        console.log('\n⏳ Waiting 3 seconds for call status to update...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Step 5: Check call details again
        console.log('5️⃣ Checking call details after waiting...');
        const callDetailsAfter = await getCallDetail(user1Token, callId);
        console.log('📋 Call details after wait:', {
            id: callDetailsAfter.call.id,
            status: callDetailsAfter.call.status,
            caller: callDetailsAfter.call.caller.username,
            callee: callDetailsAfter.call.callee.username
        });

        // Step 6: Test call answer with current status
        console.log('\n6️⃣ Testing call answer...');
        await answerCall(user2Token, callId);

        console.log('\n🎉 Modal fix test completed successfully!');
        console.log('\n📝 Summary:');
        console.log('- The frontend should now show ActiveCallModal for the caller');
        console.log('- The answer API should work with the current call status');

    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        process.exit(0);
    }
}

// Run the test
testModalFix();
