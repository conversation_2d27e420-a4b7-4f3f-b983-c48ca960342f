// test-call-flow-debug.js
// This script helps diagnose and test the call flow issues

const socket = require('socket.io-client');
const axios = require('axios');

// Configuration
const SOCKET_SERVER_URL = 'http://localhost:3001'; // Adjust to your socket server URL
const DJANGO_API_URL = 'http://localhost:8000/api'; // Adjust to your Django API URL

// Test users (replace with actual test users from your system)
const CALLER_TOKEN = 'caller-auth-token'; // Replace with actual token
const CALLEE_TOKEN = 'callee-auth-token'; // Replace with actual token

// Connect to socket server as caller
const callerSocket = socket(SOCKET_SERVER_URL, {
  query: { token: CALLER_TOKEN },
  transports: ['websocket'],
  reconnection: true,
  reconnectionAttempts: 5,
});

// Connect to socket server as callee
const calleeSocket = socket(SOCKET_SERVER_URL, {
  query: { token: CALLEE_TOKEN },
  transports: ['websocket'],
  reconnection: true,
  reconnectionAttempts: 5,
});

// Track call state
let currentCallId = null;
let callerConnected = false;
let calleeConnected = false;
let callRoomJoined = false;

// Debug helpers
function logRoomMembers(socket, roomName) {
  socket.emit('debug_room_members', { room: roomName }, (response) => {
    console.log(`🔍 Room ${roomName} members:`, response);
  });
}

// Set up caller socket event listeners
callerSocket.on('connect', () => {
  console.log('🔌 Caller socket connected with ID:', callerSocket.id);
  callerConnected = true;
  if (callerConnected && calleeConnected) {
    startTest();
  }
});

callerSocket.on('disconnect', (reason) => {
  console.log('🔌 Caller socket disconnected:', reason);
  callerConnected = false;
});

callerSocket.on('connect_error', (error) => {
  console.error('🔌 Caller socket connection error:', error);
});

callerSocket.on('call_initiated', (data) => {
  console.log('📞 Call initiated:', data);
  currentCallId = data.callId;
});

callerSocket.on('call_ringing', (data) => {
  console.log('📞 Call ringing:', data);
});

callerSocket.on('call_answered', (data) => {
  console.log('📞 Call answered:', data);
  // Check if we're in the call room
  if (currentCallId) {
    logRoomMembers(callerSocket, `call:${currentCallId}`);
  }
  // Simulate WebRTC offer after call is answered
  setTimeout(() => simulateWebRTCOffer(), 1000);
});

callerSocket.on('call_active', (data) => {
  console.log('📞 Call active event received by caller:', data);
  console.log('✅ TEST PASSED: call_active event received by caller');
});

callerSocket.on('webrtc_answer', (data) => {
  console.log('🔄 WebRTC answer received by caller:', data);
});

callerSocket.on('webrtc_ice_candidate', (data) => {
  console.log('❄️ ICE candidate received by caller:', data);
});

// Set up callee socket event listeners
calleeSocket.on('connect', () => {
  console.log('🔌 Callee socket connected with ID:', calleeSocket.id);
  calleeConnected = true;
  if (callerConnected && calleeConnected) {
    startTest();
  }
});

calleeSocket.on('disconnect', (reason) => {
  console.log('🔌 Callee socket disconnected:', reason);
  calleeConnected = false;
});

calleeSocket.on('connect_error', (error) => {
  console.error('🔌 Callee socket connection error:', error);
});

calleeSocket.on('incoming_call', (data) => {
  console.log('📞 Incoming call received by callee:', data);
  currentCallId = data.callId;
  // Simulate answering the call after a short delay
  setTimeout(() => answerCall(), 2000);
});

calleeSocket.on('webrtc_offer', (data) => {
  console.log('🔄 WebRTC offer received by callee:', data);
  // Check if we're in the call room
  if (currentCallId) {
    logRoomMembers(calleeSocket, `call:${currentCallId}`);
  }
  // Simulate WebRTC answer
  setTimeout(() => simulateWebRTCAnswer(), 1000);
});

calleeSocket.on('call_active', (data) => {
  console.log('📞 Call active event received by callee:', data);
  console.log('✅ TEST PASSED: call_active event received by callee');
});

calleeSocket.on('webrtc_ice_candidate', (data) => {
  console.log('❄️ ICE candidate received by callee:', data);
});

// Helper functions
async function startTest() {
  console.log('🧪 Starting call flow test...');
  try {
    // Initiate call via Django API
    const response = await axios.post(
      `${DJANGO_API_URL}/calling/initiate/`,
      {
        conversation_id: 'test-conversation-id', // Replace with actual conversation ID
        call_type: 'video',
      },
      {
        headers: {
          Authorization: `Bearer ${CALLER_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('📞 Call initiated via API:', response.data);
  } catch (error) {
    console.error('❌ Failed to initiate call:', error.response?.data || error.message);
  }
}

async function answerCall() {
  if (!currentCallId) {
    console.error('❌ No active call to answer');
    return;
  }

  try {
    // Answer call via Django API
    const response = await axios.post(
      `${DJANGO_API_URL}/calling/${currentCallId}/answer/`,
      {},
      {
        headers: {
          Authorization: `Bearer ${CALLEE_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('📞 Call answered via API:', response.data);
    
    // Check if we're in the call room
    if (currentCallId) {
      setTimeout(() => {
        logRoomMembers(calleeSocket, `call:${currentCallId}`);
      }, 1000);
    }
  } catch (error) {
    console.error('❌ Failed to answer call:', error.response?.data || error.message);
  }
}

function simulateWebRTCOffer() {
  if (!currentCallId) {
    console.error('❌ No active call for WebRTC offer');
    return;
  }

  // Create a mock SDP offer
  const mockOffer = {
    type: 'offer',
    sdp: 'v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0\r\na=msid-semantic: WMS\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:mock\r\na=ice-pwd:mockpwd\r\na=fingerprint:sha-256 AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99\r\na=setup:actpass\r\na=mid:0\r\na=sendrecv\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=fmtp:111 minptime=10;useinbandfec=1\r\n',
  };

  // Emit WebRTC offer
  console.log('🔄 Sending WebRTC offer from caller for call:', currentCallId);
  callerSocket.emit('webrtc_offer', {
    callId: currentCallId,
    offer: mockOffer,
  });

  // Also send an ICE candidate
  setTimeout(() => {
    const mockIceCandidate = {
      candidate: 'candidate:1 1 UDP 2122194687 ************* 53100 typ host',
      sdpMid: '0',
      sdpMLineIndex: 0,
    };
    
    console.log('❄️ Sending ICE candidate from caller for call:', currentCallId);
    callerSocket.emit('webrtc_ice_candidate', {
      callId: currentCallId,
      candidate: mockIceCandidate,
    });
  }, 500);
}

function simulateWebRTCAnswer() {
  if (!currentCallId) {
    console.error('❌ No active call for WebRTC answer');
    return;
  }

  // Create a mock SDP answer
  const mockAnswer = {
    type: 'answer',
    sdp: 'v=0\r\no=- 987654321 1 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0\r\na=msid-semantic: WMS\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:mock\r\na=ice-pwd:mockpwd\r\na=fingerprint:sha-256 AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99\r\na=setup:active\r\na=mid:0\r\na=sendrecv\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=fmtp:111 minptime=10;useinbandfec=1\r\n',
  };

  // Emit WebRTC answer
  console.log('🔄 Sending WebRTC answer from callee for call:', currentCallId);
  calleeSocket.emit('webrtc_answer', {
    callId: currentCallId,
    answer: mockAnswer,
  });

  // Also send an ICE candidate
  setTimeout(() => {
    const mockIceCandidate = {
      candidate: 'candidate:1 1 UDP 2122194687 ************* 53200 typ host',
      sdpMid: '0',
      sdpMLineIndex: 0,
    };
    
    console.log('❄️ Sending ICE candidate from callee for call:', currentCallId);
    calleeSocket.emit('webrtc_ice_candidate', {
      callId: currentCallId,
      candidate: mockIceCandidate,
    });
  }, 500);
}

// Add debug endpoint to socket server
function addDebugEndpoint() {
  // This function is just a reminder to add the debug endpoint to the socket server
  console.log('Remember to add this debug endpoint to your socket server:');
  console.log(`
  // Add this to your socket server's event handlers
  socket.on('debug_room_members', async (data, callback) => {
    try {
      const { room } = data;
      const roomMembers = this.io.sockets.adapter.rooms.get(room);
      const members = roomMembers ? Array.from(roomMembers) : [];
      callback({ success: true, room, members });
    } catch (error) {
      console.error('Error getting room members:', error);
      callback({ success: false, error: error.message });
    }
  });
  `);
}

// Clean up on process exit
process.on('SIGINT', () => {
  console.log('Cleaning up and exiting...');
  callerSocket.disconnect();
  calleeSocket.disconnect();
  process.exit(0);
});

// Print reminder about debug endpoint
addDebugEndpoint();