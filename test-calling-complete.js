#!/usr/bin/env node
/**
 * Complete Calling API Testing Script
 * Tests all calling functionality endpoints with real call flows
 */

const axios = require('axios').default;

const BASE_URL = 'http://localhost:6000/api';

// Test users credentials
const USER1 = { email: '<EMAIL>', password: 'TestPassword123!' };
const USER2 = { email: '<EMAIL>', password: 'TestPassword123!' };

let user1Token, user2Token, conversationId;

async function login(userCredentials) {
    try {
        const response = await axios.post(`${BASE_URL}/auth/login/`, userCredentials);
        return response.data.data.tokens.access;
    } catch (error) {
        console.error('Login failed:', error.response?.data || error.message);
        throw error;
    }
}

async function createConversation(token, otherUserId) {
    try {
        const response = await axios.post(
            `${BASE_URL}/messaging/conversations/create/`,
            {
                name: "",
                type: "DIRECT",
                participantIds: [otherUserId]
            },
            {
                headers: { 'Authorization': `Bearer ${token}` }
            }
        );
        return response.data.id;
    } catch (error) {
        if (error.response?.status === 400 && error.response.data.error?.includes('already exists')) {
            // Get existing conversation
            const conversations = await axios.get(`${BASE_URL}/messaging/conversations/`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            const directConversation = conversations.data.results.find(conv => 
                conv.type === 'DIRECT' && conv.participants.length === 2
            );
            if (directConversation) {
                return directConversation.id;
            }
        }
        console.error('Create conversation failed:', error.response?.data || error.message);
        throw error;
    }
}

async function initiateCall(token, conversationId, callType = 'audio') {
    try {
        const response = await axios.post(
            `${BASE_URL}/calling/initiate/`,
            { conversationId, callType },
            { headers: { 'Authorization': `Bearer ${token}` } }
        );
        return response.data;
    } catch (error) {
        console.error('Initiate call failed:', error.response?.data || error.message);
        throw error;
    }
}

async function answerCall(token, callId) {
    try {
        const response = await axios.post(
            `${BASE_URL}/calling/${callId}/answer/`,
            {},
            { headers: { 'Authorization': `Bearer ${token}` } }
        );
        return response.data;
    } catch (error) {
        console.error('Answer call failed:', error.response?.data || error.message);
        throw error;
    }
}

async function declineCall(token, callId) {
    try {
        const response = await axios.post(
            `${BASE_URL}/calling/${callId}/decline/`,
            {},
            { headers: { 'Authorization': `Bearer ${token}` } }
        );
        return response.data;
    } catch (error) {
        console.error('Decline call failed:', error.response?.data || error.message);
        throw error;
    }
}

async function endCall(token, callId) {
    try {
        const response = await axios.post(
            `${BASE_URL}/calling/${callId}/end/`,
            {},
            { headers: { 'Authorization': `Bearer ${token}` } }
        );
        return response.data;
    } catch (error) {
        console.error('End call failed:', error.response?.data || error.message);
        throw error;
    }
}

async function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function runTests() {
    console.log('🚀 Starting Complete Calling API Tests...\n');

    try {
        // Step 1: Login both users
        console.log('1. Logging in users...');
        user1Token = await login(USER1);
        user2Token = await login(USER2);
        console.log('✅ Both users logged in successfully\n');

        // Step 2: Create conversation
        console.log('2. Creating conversation...');
        // Get user2 ID from login response
        const user2LoginResponse = await axios.post(`${BASE_URL}/auth/login/`, USER2);
        const user2Id = user2LoginResponse.data.data.user.id;
        
        conversationId = await createConversation(user1Token, user2Id);
        console.log('✅ Conversation created:', conversationId, '\n');

        // Test 1: Complete call flow - Initiate → Answer → End
        console.log('📞 TEST 1: Complete Call Flow (Initiate → Answer → End)');
        console.log('----------------------------------------');
        
        console.log('  • User1 initiates call...');
        const call1 = await initiateCall(user1Token, conversationId, 'audio');
        console.log('  ✅ Call initiated:', call1.id);
        
        await sleep(1000); // Brief pause to simulate real timing
        
        console.log('  • User2 answers call...');
        const answeredCall = await answerCall(user2Token, call1.id);
        console.log('  ✅ Call answered, status:', answeredCall.status);
        
        await sleep(2000); // Simulate some call time
        
        console.log('  • User1 ends call...');
        await endCall(user1Token, call1.id);
        console.log('  ✅ Call ended successfully\n');

        // Test 2: Call decline flow - Initiate → Decline
        console.log('📞 TEST 2: Call Decline Flow (Initiate → Decline)');
        console.log('----------------------------------------');
        
        console.log('  • User1 initiates call...');
        const call2 = await initiateCall(user1Token, conversationId, 'video');
        console.log('  ✅ Call initiated:', call2.id);
        
        await sleep(1000); // Brief pause
        
        console.log('  • User2 declines call...');
        await declineCall(user2Token, call2.id);
        console.log('  ✅ Call declined successfully\n');

        // Test 3: Early end call (end before answer)
        console.log('📞 TEST 3: Early End Call Flow (Initiate → End)');
        console.log('----------------------------------------');
        
        console.log('  • User1 initiates call...');
        const call3 = await initiateCall(user1Token, conversationId, 'audio');
        console.log('  ✅ Call initiated:', call3.id);
        
        await sleep(1000); // Brief pause
        
        console.log('  • User1 ends call before answer...');
        await endCall(user1Token, call3.id);
        console.log('  ✅ Call ended early successfully\n');

        // Test 4: Different call types
        console.log('📞 TEST 4: Video Call Flow');
        console.log('----------------------------------------');
        
        console.log('  • User2 initiates video call...');
        const call4 = await initiateCall(user2Token, conversationId, 'video');
        console.log('  ✅ Video call initiated:', call4.id);
        
        await sleep(1000);
        
        console.log('  • User1 answers video call...');
        const answeredVideoCall = await answerCall(user1Token, call4.id);
        console.log('  ✅ Video call answered, status:', answeredVideoCall.status);
        
        await sleep(1000);
        
        console.log('  • User2 ends video call...');
        await endCall(user2Token, call4.id);
        console.log('  ✅ Video call ended successfully\n');

        console.log('🎉 ALL TESTS PASSED! 🎉');
        console.log('=====================================');
        console.log('✅ Initiate Call API - Working');
        console.log('✅ Answer Call API - Working');
        console.log('✅ Decline Call API - Working');
        console.log('✅ End Call API - Working');
        console.log('✅ Audio Calls - Working');
        console.log('✅ Video Calls - Working');
        console.log('✅ Complete Call Flows - Working');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

// Working curl commands for reference
console.log(`
📋 WORKING CURL COMMANDS FOR CALLING API:
=========================================

# 1. Login to get JWT token:
curl -X POST http://localhost:6000/api/auth/login/ \\
  -H "Content-Type: application/json" \\
  --data-raw '{"email": "<EMAIL>", "password": "TestPassword123!"}'

# 2. Create conversation (replace USER2_ID with actual user ID):
curl -X POST http://localhost:6000/api/messaging/conversations/create/ \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -H "Content-Type: application/json" \\
  --data-raw '{"name": "", "type": "DIRECT", "participantIds": ["USER2_ID"]}'

# 3. Initiate call (replace CONVERSATION_ID):
curl -X POST http://localhost:6000/api/calling/initiate/ \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -H "Content-Type: application/json" \\
  --data-raw '{"conversationId": "CONVERSATION_ID", "callType": "audio"}'

# 4. Answer call (replace CALL_ID):
curl -X POST http://localhost:6000/api/calling/CALL_ID/answer/ \\
  -H "Authorization: Bearer CALLEE_JWT_TOKEN" \\
  -H "Content-Type: application/json"

# 5. Decline call (replace CALL_ID):
curl -X POST http://localhost:6000/api/calling/CALL_ID/decline/ \\
  -H "Authorization: Bearer CALLEE_JWT_TOKEN" \\
  -H "Content-Type: application/json"

# 6. End call (replace CALL_ID):
curl -X POST http://localhost:6000/api/calling/CALL_ID/end/ \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -H "Content-Type: application/json"
`);

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    login,
    createConversation,
    initiateCall,
    answerCall,
    declineCall,
    endCall,
    runTests
};
